#%% md
# Comprehensive Exploratory Data Analysis (EDA)
## Russian Orders Dataset (January - June 2025)

This notebook provides a comprehensive analysis of the orders dataset containing 6 months of transaction data from Russia.

### Dataset Overview
- **Time Period**: January 2025 - June 2025
- **Geographic Scope**: Russia
- **Total Records**: ~242,000 orders
- **Data Files**: 6 monthly CSV files

### Analysis Structure
1. **Data Overview**: Load and examine dataset structure
2. **Data Quality Assessment**: Missing values, duplicates, outliers
3. **Univariate Analysis**: Individual variable distributions
4. **Bivariate Analysis**: Relationships between variables
5. **Multivariate Analysis**: Complex interactions
6. **Key Insights**: Summary of findings
#%%
# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
from datetime import datetime
import glob
from scipy import stats
from scipy.stats import chi2_contingency

# Configure display settings
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
warnings.filterwarnings('ignore')

# Set figure size defaults
plt.rcParams['figure.figsize'] = (12, 8)

print("✅ Libraries imported successfully!")
#%% md
## 1. Data Loading and Initial Overview
#%%
# Load all CSV files
data_files = glob.glob('Data/orders_info_ru_*.csv')
data_files.sort()

print(f"📁 Found {len(data_files)} data files:")
for file in data_files:
    print(f"  - {file}")

# Load and combine all data
dfs = []
file_stats = []

for file in data_files:
    df_temp = pd.read_csv(file)
    # Extract month from filename for tracking
    month = file.split('_')[-1].split('-')[0]
    df_temp['source_month'] = month
    dfs.append(df_temp)
    file_stats.append({'File': file, 'Records': len(df_temp), 'Month': month})
    print(f"📊 Loaded {file}: {len(df_temp):,} records")

# Combine all dataframes
df = pd.concat(dfs, ignore_index=True)
print(f"\n🎯 Total combined dataset: {len(df):,} records")

# Display file statistics
file_stats_df = pd.DataFrame(file_stats)
print("\n📈 Monthly Data Distribution:")
display(file_stats_df)
#%%
# Basic dataset information
print("=== 📋 DATASET OVERVIEW ===")
print(f"Shape: {df.shape}")
print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
print(f"Columns: {len(df.columns)}")
print(f"Rows: {len(df):,}")

print("\n=== 🔍 COLUMN INFORMATION ===")
df.info()
#%%
# Display first few rows and column details
print("=== 👀 FIRST 5 ROWS ===")
display(df.head())

print("\n=== 📝 COLUMN NAMES ===")
for i, col in enumerate(df.columns, 1):
    print(f"{i:2d}. {col}")
#%%
# Data types analysis
print("=== 🏷️ DATA TYPES SUMMARY ===")
dtype_counts = df.dtypes.value_counts()
print(dtype_counts)

print("\n=== 📊 DETAILED DATA TYPES ===")
type_info = []
for col in df.columns:
    type_info.append({
        'Column': col,
        'Type': str(df[col].dtype),
        'Non-null': df[col].count(),
        'Null': df[col].isnull().sum(),
        'Null %': f"{(df[col].isnull().sum() / len(df)) * 100:.1f}%"
    })

type_info_df = pd.DataFrame(type_info)
display(type_info_df)
#%% md
## 2. Data Quality Assessment
#%%
# Missing values analysis
print("=== ❌ MISSING VALUES ANALYSIS ===")
missing_data = pd.DataFrame({
    'Column': df.columns,
    'Missing_Count': df.isnull().sum(),
    'Missing_Percentage': (df.isnull().sum() / len(df)) * 100
})
missing_data = missing_data[missing_data['Missing_Count'] > 0].sort_values('Missing_Percentage', ascending=False)

if len(missing_data) > 0:
    print(f"Found {len(missing_data)} columns with missing values:")
    display(missing_data)
    
    # Visualize missing data
    plt.figure(figsize=(12, 6))
    sns.barplot(data=missing_data, x='Missing_Percentage', y='Column')
    plt.title('Missing Data Percentage by Column')
    plt.xlabel('Missing Percentage (%)')
    plt.tight_layout()
    plt.show()
else:
    print("✅ No missing values found in the dataset!")
#%%
# Duplicate analysis
print("=== 🔄 DUPLICATE ANALYSIS ===")
total_duplicates = df.duplicated().sum()
print(f"Total duplicate rows: {total_duplicates:,}")

# Check for duplicate IDs (should be unique)
duplicate_ids = df['id'].duplicated().sum()
print(f"Duplicate order IDs: {duplicate_ids:,}")

if duplicate_ids > 0:
    print("\n⚠️ Sample duplicate IDs:")
    duplicate_id_values = df[df['id'].duplicated(keep=False)]['id'].unique()[:5]
    for dup_id in duplicate_id_values:
        print(f"ID {dup_id} appears {(df['id'] == dup_id).sum()} times")
else:
    print("✅ All order IDs are unique!")

# Check for other potential duplicates
print("\n=== 🔍 OTHER DUPLICATE CHECKS ===")
print(f"Unique sellers: {df['seller_id'].nunique():,}")
print(f"Unique buyers: {df['buyer_id'].nunique():,}")
print(f"Unique offers: {df['offer_id'].nunique():,}")
print(f"Unique regions: {df['region_id'].nunique():,}")
print(f"Unique categories: {df['category_id'].nunique():,}")
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}