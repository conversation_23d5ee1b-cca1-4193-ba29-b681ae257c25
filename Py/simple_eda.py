#!/usr/bin/env python3
"""
Simplified Comprehensive EDA
Russian Orders Dataset (January - June 2025)

This script performs a complete exploratory data analysis using only basic libraries.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
import glob
from scipy import stats

# Configure settings
warnings.filterwarnings('ignore')
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

def load_and_analyze_data():
    """Complete EDA pipeline"""
    print("🚀 Starting Comprehensive EDA Analysis")
    print("=" * 60)
    
    # Load data
    print("\n📊 Loading data...")
    data_files = glob.glob('Data/orders_info_ru_*.csv')
    data_files.sort()
    
    print(f"Found {len(data_files)} data files:")
    for file in data_files:
        print(f"  - {file}")
    
    dfs = []
    expected_columns = None

    for file in data_files:
        df_temp = pd.read_csv(file)

        # Handle inconsistent columns (June file has extra column)
        if expected_columns is None:
            expected_columns = df_temp.columns.tolist()
        else:
            # If columns don't match, keep only the expected ones
            if len(df_temp.columns) != len(expected_columns):
                df_temp = df_temp[expected_columns]

        month = file.split('_')[-1].split('-')[0]
        df_temp['source_month'] = month
        dfs.append(df_temp)
        print(f"Loaded {file}: {len(df_temp):,} records")
    
    df = pd.concat(dfs, ignore_index=True)
    print(f"\n🎯 Total combined dataset: {len(df):,} records")
    
    # Basic info
    print("\n=== 📋 DATASET OVERVIEW ===")
    print(f"Shape: {df.shape}")
    print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    print(f"Columns: {len(df.columns)}")
    
    # Data types
    print("\n=== 🏷️ DATA TYPES ===")
    print(df.dtypes.value_counts())
    
    # Missing values
    print("\n=== ❌ MISSING VALUES ===")
    missing = df.isnull().sum()
    missing = missing[missing > 0]
    if len(missing) > 0:
        print("Columns with missing values:")
        for col, count in missing.items():
            print(f"  {col}: {count:,} ({count/len(df)*100:.1f}%)")
    else:
        print("✅ No missing values found!")
    
    # Duplicates
    print("\n=== 🔄 DUPLICATES ===")
    duplicates = df.duplicated().sum()
    duplicate_ids = df['id'].duplicated().sum()
    print(f"Duplicate rows: {duplicates:,}")
    print(f"Duplicate IDs: {duplicate_ids:,}")
    
    # Preprocessing
    print("\n=== 🔧 PREPROCESSING ===")

    # Convert numerical columns
    df['sum'] = pd.to_numeric(df['sum'], errors='coerce')
    df['views_count'] = pd.to_numeric(df['views_count'], errors='coerce')
    df['success_fee'] = pd.to_numeric(df['success_fee'], errors='coerce')
    df['seller_rating'] = pd.to_numeric(df['seller_rating'], errors='coerce')
    df['buyer_rating'] = pd.to_numeric(df['buyer_rating'], errors='coerce')

    # Convert date column
    df['order_date'] = pd.to_datetime(df['order_date'], format='%d.%m.%Y', errors='coerce')
    df['month'] = df['order_date'].dt.month
    df['weekday'] = df['order_date'].dt.day_name()
    df['is_weekend'] = df['order_date'].dt.weekday >= 5

    # Convert boolean columns
    bool_columns = ['order_cancelled', 'auction_winning', 'seller_has_block', 'buyer_has_block', 'refunded', 'is_created_by_api']
    for col in bool_columns:
        if col in df.columns:
            df[col] = df[col].map({'yes': True, 'no': False}).fillna(False)

    print(f"Date range: {df['order_date'].min()} to {df['order_date'].max()}")
    print(f"Numerical conversion completed. Invalid values converted to NaN.")

    # Clean extreme outliers in order amounts (likely data errors)
    q99 = df['sum'].quantile(0.99)
    extreme_outliers = df['sum'] > q99 * 10  # Values more than 10x the 99th percentile
    print(f"Removing {extreme_outliers.sum()} extreme outliers (>10x 99th percentile)")
    df.loc[extreme_outliers, 'sum'] = np.nan
    
    return df

def univariate_analysis(df):
    """Analyze individual variables"""
    print("\n=== 📊 UNIVARIATE ANALYSIS ===")
    
    # Order amounts
    print("\n💰 ORDER AMOUNTS:")
    # Calculate statistics on non-null values
    sum_clean = df['sum'].dropna()
    print(f"  Valid records: {len(sum_clean):,} / {len(df):,}")
    print(f"  Mean: {sum_clean.mean():,.2f}")
    print(f"  Median: {sum_clean.median():,.2f}")
    print(f"  Std Dev: {sum_clean.std():,.2f}")
    print(f"  Min: {sum_clean.min():,.2f}")
    print(f"  Max: {sum_clean.max():,.2f}")
    print(f"  Skewness: {stats.skew(sum_clean):.2f}")
    
    # Create visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Order amount distribution (remove NaN values)
    sum_clean = df['sum'].dropna()
    axes[0,0].hist(sum_clean, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].set_title('Order Amount Distribution')
    axes[0,0].set_xlabel('Order Amount')
    axes[0,0].set_ylabel('Frequency')

    # Log scale
    axes[0,1].hist(np.log1p(sum_clean), bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0,1].set_title('Order Amount Distribution (Log Scale)')
    axes[0,1].set_xlabel('Log(Order Amount + 1)')
    axes[0,1].set_ylabel('Frequency')

    # Box plot
    axes[1,0].boxplot(sum_clean)
    axes[1,0].set_title('Order Amount Box Plot')
    axes[1,0].set_ylabel('Order Amount')

    # Views count
    views_clean = df['views_count'].dropna()
    axes[1,1].hist(views_clean, bins=50, alpha=0.7, color='orange', edgecolor='black')
    axes[1,1].set_title('Views Count Distribution')
    axes[1,1].set_xlabel('Views Count')
    axes[1,1].set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig('Pics/univariate_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Views statistics
    print(f"\n👀 VIEWS COUNT:")
    print(f"  Mean: {df['views_count'].mean():.2f}")
    print(f"  Median: {df['views_count'].median():.2f}")
    print(f"  Max: {df['views_count'].max()}")
    print(f"  Zero views: {(df['views_count'] == 0).sum():,} ({(df['views_count'] == 0).mean()*100:.1f}%)")

def bivariate_analysis(df):
    """Analyze relationships between variables"""
    print("\n=== 🔗 BIVARIATE ANALYSIS ===")
    
    # Correlation analysis
    numerical_cols = ['sum', 'views_count', 'seller_rating', 'buyer_rating', 'success_fee']
    numerical_cols = [col for col in numerical_cols if col in df.columns]
    
    corr_matrix = df[numerical_cols].corr()
    
    plt.figure(figsize=(10, 8))
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, square=True)
    plt.title('Correlation Matrix')
    plt.tight_layout()
    plt.savefig('Pics/correlation_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Temporal analysis
    monthly_stats = df.groupby('month').agg({
        'sum': ['count', 'mean', 'sum'],
        'order_cancelled': 'mean',
        'refunded': 'mean'
    }).round(2)
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Orders per month
    monthly_orders = df.groupby('month').size()
    axes[0,0].bar(monthly_orders.index, monthly_orders.values, color='skyblue')
    axes[0,0].set_title('Orders by Month')
    axes[0,0].set_xlabel('Month')
    axes[0,0].set_ylabel('Number of Orders')
    
    # Average order value by month
    monthly_avg = df.groupby('month')['sum'].mean()
    axes[0,1].plot(monthly_avg.index, monthly_avg.values, marker='o', color='green', linewidth=2)
    axes[0,1].set_title('Average Order Value by Month')
    axes[0,1].set_xlabel('Month')
    axes[0,1].set_ylabel('Average Order Value')
    
    # Weekday analysis
    weekday_orders = df.groupby('weekday').size()
    weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    weekday_orders = weekday_orders.reindex(weekday_order)
    axes[1,0].bar(range(len(weekday_orders)), weekday_orders.values, color='orange')
    axes[1,0].set_title('Orders by Day of Week')
    axes[1,0].set_xlabel('Day of Week')
    axes[1,0].set_ylabel('Number of Orders')
    axes[1,0].set_xticks(range(len(weekday_order)))
    axes[1,0].set_xticklabels(weekday_order, rotation=45)
    
    # Cancellation rate by month
    cancel_by_month = df.groupby('month')['order_cancelled'].mean() * 100
    axes[1,1].bar(cancel_by_month.index, cancel_by_month.values, color='red', alpha=0.7)
    axes[1,1].set_title('Cancellation Rate by Month (%)')
    axes[1,1].set_xlabel('Month')
    axes[1,1].set_ylabel('Cancellation Rate (%)')
    
    plt.tight_layout()
    plt.savefig('Pics/temporal_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n📅 TEMPORAL INSIGHTS:")
    print(f"  Peak month: {monthly_orders.idxmax()} ({monthly_orders.max():,} orders)")
    print(f"  Lowest month: {monthly_orders.idxmin()} ({monthly_orders.min():,} orders)")
    
    # Weekend vs weekday
    weekend_avg = df[df['is_weekend']]['sum'].mean()
    weekday_avg = df[~df['is_weekend']]['sum'].mean()
    print(f"  Weekend avg order: {weekend_avg:.2f}")
    print(f"  Weekday avg order: {weekday_avg:.2f}")

def regional_analysis(df):
    """Analyze regional patterns"""
    print("\n=== 🌍 REGIONAL ANALYSIS ===")
    
    regional_stats = df.groupby('region_id').agg({
        'sum': ['count', 'mean', 'sum'],
        'order_cancelled': 'mean'
    }).round(2)
    
    regional_stats.columns = ['Order_Count', 'Avg_Order_Value', 'Total_Revenue', 'Cancel_Rate']
    regional_stats = regional_stats.sort_values('Order_Count', ascending=False)
    
    # Top 15 regions
    top_regions = regional_stats.head(15)
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Top regions by order count
    axes[0,0].barh(range(len(top_regions)), top_regions['Order_Count'], color='lightblue')
    axes[0,0].set_title('Top 15 Regions by Order Count')
    axes[0,0].set_xlabel('Number of Orders')
    axes[0,0].set_yticks(range(len(top_regions)))
    axes[0,0].set_yticklabels(top_regions.index)
    
    # Top regions by revenue
    top_revenue = regional_stats.sort_values('Total_Revenue', ascending=False).head(15)
    axes[0,1].barh(range(len(top_revenue)), top_revenue['Total_Revenue'], color='lightgreen')
    axes[0,1].set_title('Top 15 Regions by Revenue')
    axes[0,1].set_xlabel('Total Revenue')
    axes[0,1].set_yticks(range(len(top_revenue)))
    axes[0,1].set_yticklabels(top_revenue.index)
    
    # Average order value
    top_avg = regional_stats.sort_values('Avg_Order_Value', ascending=False).head(15)
    axes[1,0].barh(range(len(top_avg)), top_avg['Avg_Order_Value'], color='orange')
    axes[1,0].set_title('Top 15 Regions by Avg Order Value')
    axes[1,0].set_xlabel('Average Order Value')
    axes[1,0].set_yticks(range(len(top_avg)))
    axes[1,0].set_yticklabels(top_avg.index)
    
    # Regional distribution pie chart
    top_10_regions = regional_stats.head(10)
    other_count = regional_stats.iloc[10:]['Order_Count'].sum()
    
    pie_data = list(top_10_regions['Order_Count']) + [other_count]
    pie_labels = list(top_10_regions.index) + ['Others']
    
    axes[1,1].pie(pie_data, labels=pie_labels, autopct='%1.1f%%', startangle=90)
    axes[1,1].set_title('Regional Distribution (Top 10 + Others)')
    
    plt.tight_layout()
    plt.savefig('Pics/regional_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n🏆 TOP REGIONS:")
    print(f"  By orders: Region {top_regions.index[0]} ({top_regions.iloc[0]['Order_Count']:,} orders)")
    print(f"  By revenue: Region {top_revenue.index[0]} ({top_revenue.iloc[0]['Total_Revenue']:,.2f} revenue)")
    print(f"  By avg value: Region {top_avg.index[0]} ({top_avg.iloc[0]['Avg_Order_Value']:.2f} avg)")

def generate_insights(df):
    """Generate key business insights"""
    print("\n=== 💡 KEY INSIGHTS ===")
    
    insights = []
    
    # Basic metrics
    total_orders = len(df)
    total_revenue = df['sum'].sum()
    avg_order_value = df['sum'].mean()
    
    insights.append(f"Dataset: {total_orders:,} orders, {total_revenue:,.2f} total revenue")
    insights.append(f"Average order value: {avg_order_value:.2f}")
    
    # Temporal insights
    monthly_orders = df.groupby('month').size()
    peak_month = monthly_orders.idxmax()
    low_month = monthly_orders.idxmin()
    insights.append(f"Peak month: {peak_month} ({monthly_orders[peak_month]:,} orders)")
    insights.append(f"Lowest month: {low_month} ({monthly_orders[low_month]:,} orders)")
    
    # Business metrics
    cancel_rate = df['order_cancelled'].mean() * 100
    refund_rate = df['refunded'].mean() * 100
    insights.append(f"Cancellation rate: {cancel_rate:.2f}%")
    insights.append(f"Refund rate: {refund_rate:.2f}%")
    
    # Auction analysis
    auction_orders = df[df['auction_winning']].shape[0]
    auction_pct = auction_orders / total_orders * 100
    insights.append(f"Auction orders: {auction_orders:,} ({auction_pct:.1f}%)")
    
    # Views impact
    zero_views = (df['views_count'] == 0).sum()
    zero_views_pct = zero_views / total_orders * 100
    insights.append(f"Zero-view orders: {zero_views:,} ({zero_views_pct:.1f}%)")
    
    # API usage
    api_orders = df[df['is_created_by_api']].shape[0]
    api_pct = api_orders / total_orders * 100
    insights.append(f"API-created orders: {api_orders:,} ({api_pct:.1f}%)")
    
    print("\n".join([f"• {insight}" for insight in insights]))
    
    return insights

def save_summary_report(df, insights):
    """Save summary report"""
    report = f"""# EDA Summary Report - Russian Orders Dataset

## Overview
- **Total Records**: {len(df):,}
- **Date Range**: {df['order_date'].min()} to {df['order_date'].max()}
- **Total Revenue**: {df['sum'].sum():,.2f}
- **Average Order Value**: {df['sum'].mean():.2f}

## Key Insights
"""
    for i, insight in enumerate(insights, 1):
        report += f"{i}. {insight}\n"
    
    report += f"""
## Data Quality
- Missing Values: {df.isnull().sum().sum()}
- Duplicate Records: {df.duplicated().sum()}
- Unique Orders: {df['id'].nunique():,}

Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open('eda_summary_report.md', 'w') as f:
        f.write(report)
    
    print("\n📄 Summary report saved as 'eda_summary_report.md'")

def main():
    """Main execution"""
    # Create Pics directory if it doesn't exist
    import os
    os.makedirs('Pics', exist_ok=True)
    
    # Load and analyze
    df = load_and_analyze_data()
    
    # Run analyses
    univariate_analysis(df)
    bivariate_analysis(df)
    regional_analysis(df)
    
    # Generate insights
    insights = generate_insights(df)
    
    # Save report
    save_summary_report(df, insights)
    
    print("\n" + "=" * 60)
    print("🎉 COMPREHENSIVE EDA COMPLETED!")
    print("📊 Visualizations saved in Pics/ directory")
    print("📄 Summary report: eda_summary_report.md")
    print("=" * 60)

if __name__ == "__main__":
    main()
