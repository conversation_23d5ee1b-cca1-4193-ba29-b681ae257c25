#!/usr/bin/env python3
"""
Comprehensive EDA Execution Script
Russian Orders Dataset (January - June 2025)

This script runs the complete exploratory data analysis on the orders dataset.
It combines all analysis functions and generates comprehensive insights.
"""

import sys
import os

# Add current directory to path
sys.path.append(os.getcwd())

# Import the analysis modules
try:
    from comprehensive_eda import *
    from eda_advanced_analysis import *
    print("✅ Successfully imported analysis modules")
except ImportError as e:
    print(f"❌ Error importing modules: {e}")
    print("Please ensure comprehensive_eda.py and eda_advanced_analysis.py are in the current directory")
    sys.exit(1)

def main():
    """Main execution function"""
    print("🚀 Starting Comprehensive EDA Analysis")
    print("=" * 60)
    
    try:
        # Step 1: Load and combine data
        print("\n📊 STEP 1: Loading and combining data...")
        df = load_and_combine_data()
        
        # Step 2: Basic dataset information
        print("\n🔍 STEP 2: Basic dataset overview...")
        basic_dataset_info(df)
        
        # Step 3: Data quality assessment
        print("\n🔧 STEP 3: Data quality assessment...")
        assess_data_quality(df)
        
        # Step 4: Data preprocessing
        print("\n⚙️ STEP 4: Data preprocessing...")
        df = preprocess_data(df)
        
        # Step 5: Summary statistics
        print("\n📈 STEP 5: Summary statistics...")
        summary_statistics(df)
        
        # Step 6: Univariate analysis
        print("\n📊 STEP 6: Univariate analysis...")
        analyze_order_amounts(df)
        analyze_views_count(df)
        
        # Step 7: Bivariate analysis
        print("\n🔗 STEP 7: Bivariate analysis...")
        correlation_analysis(df)
        temporal_analysis(df)
        categorical_analysis(df)
        regional_analysis(df)
        
        # Step 8: Multivariate analysis
        print("\n🎯 STEP 8: Multivariate analysis...")
        multivariate_analysis(df)
        
        # Step 9: Generate insights
        print("\n💡 STEP 9: Generating key insights...")
        insights = generate_insights(df)
        
        # Step 10: Save summary report
        print("\n📝 STEP 10: Saving summary report...")
        save_summary_report(df, insights)
        
        print("\n" + "=" * 60)
        print("🎉 COMPREHENSIVE EDA COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        return df, insights
        
    except Exception as e:
        print(f"\n❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def save_summary_report(df, insights):
    """Save a summary report of the analysis"""
    report_content = f"""
# EDA Summary Report
## Russian Orders Dataset Analysis

### Dataset Overview
- **Total Records**: {len(df):,}
- **Date Range**: {df['order_date'].min()} to {df['order_date'].max()}
- **Total Revenue**: {df['sum'].sum():,.2f}
- **Average Order Value**: {df['sum'].mean():.2f}

### Key Insights
"""
    
    for i, insight in enumerate(insights, 1):
        report_content += f"{i}. {insight}\n"
    
    report_content += f"""

### Data Quality Summary
- **Missing Values**: {df.isnull().sum().sum()} total missing values
- **Duplicate Records**: {df.duplicated().sum()} duplicate rows
- **Unique Orders**: {df['id'].nunique():,}
- **Unique Customers**: {df['buyer_id'].nunique():,}
- **Unique Sellers**: {df['seller_id'].nunique():,}

### Analysis Completion
- ✅ Data loading and preprocessing
- ✅ Data quality assessment
- ✅ Univariate analysis
- ✅ Bivariate analysis
- ✅ Multivariate analysis
- ✅ Key insights generation

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open('eda_summary_report.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("📄 Summary report saved as 'eda_summary_report.md'")

if __name__ == "__main__":
    # Check if data directory exists
    if not os.path.exists('Data'):
        print("❌ Data directory not found!")
        print("Please ensure the 'Data' directory with CSV files exists in the current directory.")
        sys.exit(1)
    
    # Check if CSV files exist
    csv_files = glob.glob('Data/orders_info_ru_*.csv')
    if not csv_files:
        print("❌ No CSV files found in Data directory!")
        print("Please ensure the CSV files are present in the Data directory.")
        sys.exit(1)
    
    print(f"✅ Found {len(csv_files)} CSV files in Data directory")
    
    # Run the analysis
    df, insights = main()
    
    if df is not None:
        print(f"\n📊 Final dataset shape: {df.shape}")
        print("🎯 Analysis artifacts created:")
        print("   - Multiple visualization plots")
        print("   - eda_summary_report.md")
        print("   - Comprehensive analysis output")
    else:
        print("❌ Analysis failed. Please check the error messages above.")
