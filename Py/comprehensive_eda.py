# Comprehensive Exploratory Data Analysis (EDA)
# Russian Orders Dataset (January - June 2025)

"""
This script provides a comprehensive analysis of the orders dataset containing 
6 months of transaction data from Russia.

Dataset Overview:
- Time Period: January 2025 - June 2025
- Geographic Scope: Russia
- Total Records: ~242,000 orders
- Data Files: 6 monthly CSV files
"""

# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
from datetime import datetime
import glob
from scipy import stats

# Configure display settings
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
warnings.filterwarnings('ignore')

# Set figure size defaults
plt.rcParams['figure.figsize'] = (12, 8)

print("Libraries imported successfully!")

# =============================================================================
# 1. DATA LOADING AND INITIAL OVERVIEW
# =============================================================================

def load_and_combine_data():
    """Load all CSV files and combine them into a single dataframe"""
    data_files = glob.glob('Data/orders_info_ru_*.csv')
    data_files.sort()
    
    print(f"Found {len(data_files)} data files:")
    for file in data_files:
        print(f"  - {file}")
    
    # Load and combine all data
    dfs = []
    for file in data_files:
        df_temp = pd.read_csv(file)
        # Extract month from filename for tracking
        month = file.split('_')[-1].split('-')[0]
        df_temp['source_month'] = month
        dfs.append(df_temp)
        print(f"Loaded {file}: {len(df_temp):,} records")
    
    # Combine all dataframes
    df = pd.concat(dfs, ignore_index=True)
    print(f"\nTotal combined dataset: {len(df):,} records")
    return df

def basic_dataset_info(df):
    """Display basic information about the dataset"""
    print("=== DATASET OVERVIEW ===")
    print(f"Shape: {df.shape}")
    print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    print("\n=== COLUMN INFORMATION ===")
    print(df.info())
    
    print("\n=== FIRST 5 ROWS ===")
    print(df.head())
    
    print("\n=== COLUMN NAMES ===")
    print(df.columns.tolist())
    
    # Data types analysis
    print("\n=== DATA TYPES SUMMARY ===")
    dtype_counts = df.dtypes.value_counts()
    print(dtype_counts)
    
    print("\n=== DETAILED DATA TYPES ===")
    for col in df.columns:
        print(f"{col:25} | {str(df[col].dtype):15} | Non-null: {df[col].count():,} | Null: {df[col].isnull().sum():,}")

# =============================================================================
# 2. DATA QUALITY ASSESSMENT
# =============================================================================

def assess_data_quality(df):
    """Assess data quality including missing values and duplicates"""
    print("=== MISSING VALUES ANALYSIS ===")
    missing_data = pd.DataFrame({
        'Column': df.columns,
        'Missing_Count': df.isnull().sum(),
        'Missing_Percentage': (df.isnull().sum() / len(df)) * 100
    })
    missing_data = missing_data[missing_data['Missing_Count'] > 0].sort_values('Missing_Percentage', ascending=False)
    
    if len(missing_data) > 0:
        print(missing_data)
        
        # Visualize missing data
        plt.figure(figsize=(12, 6))
        sns.barplot(data=missing_data, x='Missing_Percentage', y='Column')
        plt.title('Missing Data Percentage by Column')
        plt.xlabel('Missing Percentage (%)')
        plt.tight_layout()
        plt.show()
    else:
        print("No missing values found in the dataset!")
    
    # Duplicate analysis
    print("\n=== DUPLICATE ANALYSIS ===")
    total_duplicates = df.duplicated().sum()
    print(f"Total duplicate rows: {total_duplicates:,}")
    
    # Check for duplicate IDs (should be unique)
    duplicate_ids = df['id'].duplicated().sum()
    print(f"Duplicate order IDs: {duplicate_ids:,}")
    
    if duplicate_ids > 0:
        print("\nSample duplicate IDs:")
        duplicate_id_values = df[df['id'].duplicated(keep=False)]['id'].unique()[:5]
        for dup_id in duplicate_id_values:
            print(f"ID {dup_id} appears {(df['id'] == dup_id).sum()} times")

def preprocess_data(df):
    """Preprocess data for analysis"""
    print("=== DATA PREPROCESSING ===")
    
    # Convert date column
    df['order_date'] = pd.to_datetime(df['order_date'], format='%d.%m.%Y', errors='coerce')
    print(f"Date conversion completed. Invalid dates: {df['order_date'].isnull().sum()}")
    
    # Extract date components
    df['year'] = df['order_date'].dt.year
    df['month'] = df['order_date'].dt.month
    df['day'] = df['order_date'].dt.day
    df['weekday'] = df['order_date'].dt.day_name()
    df['is_weekend'] = df['order_date'].dt.weekday >= 5
    
    # Convert boolean-like columns
    bool_columns = ['order_cancelled', 'auction_winning', 'seller_has_block', 'buyer_has_block', 'refunded', 'is_created_by_api']
    for col in bool_columns:
        if col in df.columns:
            df[col] = df[col].map({'yes': True, 'no': False})
    
    print("Boolean columns converted successfully")
    print(f"Date range: {df['order_date'].min()} to {df['order_date'].max()}")
    
    return df

# =============================================================================
# 3. SUMMARY STATISTICS
# =============================================================================

def summary_statistics(df):
    """Generate summary statistics for numerical and categorical columns"""
    print("=== NUMERICAL COLUMNS SUMMARY ===")
    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    print(f"Numerical columns: {numerical_cols}")
    
    # Basic statistics
    print(df[numerical_cols].describe())
    
    print("\n=== CATEGORICAL COLUMNS SUMMARY ===")
    categorical_cols = df.select_dtypes(include=['object', 'bool']).columns.tolist()
    categorical_cols = [col for col in categorical_cols if col not in ['seller_email', 'buyer_email', 'seller_phone', 'buyer_phone']]
    
    for col in categorical_cols[:10]:  # Show first 10 to avoid overwhelming output
        print(f"\n{col.upper()}:")
        value_counts = df[col].value_counts()
        print(f"  Unique values: {df[col].nunique()}")
        print(f"  Most common: {value_counts.head(3).to_dict()}")
        if df[col].nunique() <= 10:
            print(f"  All values: {value_counts.to_dict()}")

# =============================================================================
# 4. UNIVARIATE ANALYSIS
# =============================================================================

def analyze_order_amounts(df):
    """Analyze order amounts distribution"""
    print("=== ORDER AMOUNT ANALYSIS ===")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Histogram
    axes[0,0].hist(df['sum'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].set_title('Distribution of Order Amounts')
    axes[0,0].set_xlabel('Order Amount')
    axes[0,0].set_ylabel('Frequency')
    
    # Log-scale histogram for better visualization
    axes[0,1].hist(np.log1p(df['sum']), bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0,1].set_title('Distribution of Order Amounts (Log Scale)')
    axes[0,1].set_xlabel('Log(Order Amount + 1)')
    axes[0,1].set_ylabel('Frequency')
    
    # Box plot
    axes[1,0].boxplot(df['sum'])
    axes[1,0].set_title('Box Plot of Order Amounts')
    axes[1,0].set_ylabel('Order Amount')
    
    # Box plot without outliers for better view
    q1 = df['sum'].quantile(0.25)
    q3 = df['sum'].quantile(0.75)
    iqr = q3 - q1
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    filtered_sum = df[(df['sum'] >= lower_bound) & (df['sum'] <= upper_bound)]['sum']
    axes[1,1].boxplot(filtered_sum)
    axes[1,1].set_title('Box Plot of Order Amounts (Outliers Removed)')
    axes[1,1].set_ylabel('Order Amount')
    
    plt.tight_layout()
    plt.show()
    
    # Statistical summary
    print(f"Order Amount Statistics:")
    print(f"  Mean: {df['sum'].mean():.2f}")
    print(f"  Median: {df['sum'].median():.2f}")
    print(f"  Std Dev: {df['sum'].std():.2f}")
    print(f"  Skewness: {stats.skew(df['sum']):.2f}")
    print(f"  Kurtosis: {stats.kurtosis(df['sum']):.2f}")
    print(f"  Min: {df['sum'].min():.2f}")
    print(f"  Max: {df['sum'].max():.2f}")

def analyze_views_count(df):
    """Analyze views count distribution"""
    print("=== VIEWS COUNT ANALYSIS ===")
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # Histogram
    axes[0].hist(df['views_count'], bins=50, alpha=0.7, color='orange', edgecolor='black')
    axes[0].set_title('Distribution of Views Count')
    axes[0].set_xlabel('Views Count')
    axes[0].set_ylabel('Frequency')
    
    # Box plot
    axes[1].boxplot(df['views_count'])
    axes[1].set_title('Box Plot of Views Count')
    axes[1].set_ylabel('Views Count')
    
    # Views count vs Order amount scatter
    sample_data = df.sample(n=min(5000, len(df)))  # Sample for performance
    axes[2].scatter(sample_data['views_count'], sample_data['sum'], alpha=0.5, color='purple')
    axes[2].set_title('Views Count vs Order Amount')
    axes[2].set_xlabel('Views Count')
    axes[2].set_ylabel('Order Amount')
    
    plt.tight_layout()
    plt.show()
    
    print(f"Views Count Statistics:")
    print(f"  Mean: {df['views_count'].mean():.2f}")
    print(f"  Median: {df['views_count'].median():.2f}")
    print(f"  Max: {df['views_count'].max()}")
    print(f"  Zero views: {(df['views_count'] == 0).sum():,} ({(df['views_count'] == 0).mean()*100:.1f}%)")

# =============================================================================
# MAIN EXECUTION
# =============================================================================

if __name__ == "__main__":
    # Load data
    df = load_and_combine_data()
    
    # Basic info
    basic_dataset_info(df)
    
    # Data quality assessment
    assess_data_quality(df)
    
    # Preprocess data
    df = preprocess_data(df)
    
    # Summary statistics
    summary_statistics(df)
    
    # Univariate analysis
    analyze_order_amounts(df)
    analyze_views_count(df)
    
    print("\n=== EDA PART 1 COMPLETED ===")
    print("Run the next script for bivariate and multivariate analysis...")
