# Advanced EDA - Bivariate and Multivariate Analysis
# Russian Orders Dataset (January - June 2025)

"""
This script continues the EDA with bivariate and multivariate analysis,
providing deeper insights into relationships between variables.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
from datetime import datetime
import glob
from scipy import stats
from scipy.stats import chi2_contingency

# Configure settings
warnings.filterwarnings('ignore')
plt.rcParams['figure.figsize'] = (12, 8)

# =============================================================================
# LOAD PREPROCESSED DATA
# =============================================================================

def load_preprocessed_data():
    """Load and preprocess data (assuming previous script was run)"""
    data_files = glob.glob('Data/orders_info_ru_*.csv')
    data_files.sort()
    
    dfs = []
    for file in data_files:
        df_temp = pd.read_csv(file)
        month = file.split('_')[-1].split('-')[0]
        df_temp['source_month'] = month
        dfs.append(df_temp)
    
    df = pd.concat(dfs, ignore_index=True)
    
    # Preprocessing
    df['order_date'] = pd.to_datetime(df['order_date'], format='%d.%m.%Y', errors='coerce')
    df['year'] = df['order_date'].dt.year
    df['month'] = df['order_date'].dt.month
    df['day'] = df['order_date'].dt.day
    df['weekday'] = df['order_date'].dt.day_name()
    df['is_weekend'] = df['order_date'].dt.weekday >= 5
    
    bool_columns = ['order_cancelled', 'auction_winning', 'seller_has_block', 'buyer_has_block', 'refunded', 'is_created_by_api']
    for col in bool_columns:
        if col in df.columns:
            df[col] = df[col].map({'yes': True, 'no': False})
    
    return df

# =============================================================================
# 5. BIVARIATE ANALYSIS
# =============================================================================

def correlation_analysis(df):
    """Analyze correlations between numerical variables"""
    print("=== CORRELATION ANALYSIS ===")
    
    # Select numerical columns
    numerical_cols = ['sum', 'views_count', 'seller_rating', 'buyer_rating', 'success_fee', 'month', 'day']
    numerical_cols = [col for col in numerical_cols if col in df.columns]
    
    # Calculate correlation matrix
    corr_matrix = df[numerical_cols].corr()
    
    # Create correlation heatmap
    plt.figure(figsize=(12, 10))
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8})
    plt.title('Correlation Matrix of Numerical Variables')
    plt.tight_layout()
    plt.show()
    
    # Print strongest correlations
    print("\nStrongest positive correlations:")
    corr_pairs = []
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j], corr_matrix.iloc[i, j]))
    
    corr_pairs.sort(key=lambda x: abs(x[2]), reverse=True)
    for var1, var2, corr in corr_pairs[:5]:
        print(f"  {var1} - {var2}: {corr:.3f}")

def temporal_analysis(df):
    """Analyze temporal patterns in orders"""
    print("=== TEMPORAL ANALYSIS ===")
    
    # Monthly trends
    monthly_stats = df.groupby('month').agg({
        'sum': ['count', 'mean', 'sum'],
        'views_count': 'mean',
        'order_cancelled': 'mean',
        'refunded': 'mean'
    }).round(2)
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Orders per month
    monthly_orders = df.groupby('month').size()
    axes[0,0].bar(monthly_orders.index, monthly_orders.values, color='skyblue')
    axes[0,0].set_title('Number of Orders by Month')
    axes[0,0].set_xlabel('Month')
    axes[0,0].set_ylabel('Number of Orders')
    
    # Average order value by month
    monthly_avg = df.groupby('month')['sum'].mean()
    axes[0,1].plot(monthly_avg.index, monthly_avg.values, marker='o', color='green', linewidth=2)
    axes[0,1].set_title('Average Order Value by Month')
    axes[0,1].set_xlabel('Month')
    axes[0,1].set_ylabel('Average Order Value')
    
    # Orders by weekday
    weekday_orders = df.groupby('weekday').size()
    weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    weekday_orders = weekday_orders.reindex(weekday_order)
    axes[1,0].bar(range(len(weekday_orders)), weekday_orders.values, color='orange')
    axes[1,0].set_title('Orders by Day of Week')
    axes[1,0].set_xlabel('Day of Week')
    axes[1,0].set_ylabel('Number of Orders')
    axes[1,0].set_xticks(range(len(weekday_order)))
    axes[1,0].set_xticklabels(weekday_order, rotation=45)
    
    # Weekend vs Weekday comparison
    weekend_comparison = df.groupby('is_weekend')['sum'].agg(['count', 'mean'])
    axes[1,1].bar(['Weekday', 'Weekend'], weekend_comparison['count'], color='purple', alpha=0.7)
    axes[1,1].set_title('Orders: Weekday vs Weekend')
    axes[1,1].set_ylabel('Number of Orders')
    
    plt.tight_layout()
    plt.show()
    
    print("Monthly Statistics:")
    print(monthly_stats)

def categorical_analysis(df):
    """Analyze relationships between categorical variables"""
    print("=== CATEGORICAL ANALYSIS ===")
    
    # Order cancellation analysis
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Cancellation rate by month
    cancel_by_month = df.groupby('month')['order_cancelled'].mean() * 100
    axes[0,0].bar(cancel_by_month.index, cancel_by_month.values, color='red', alpha=0.7)
    axes[0,0].set_title('Order Cancellation Rate by Month (%)')
    axes[0,0].set_xlabel('Month')
    axes[0,0].set_ylabel('Cancellation Rate (%)')
    
    # Auction winning vs regular orders
    auction_stats = df.groupby('auction_winning')['sum'].agg(['count', 'mean'])
    axes[0,1].bar(['Regular', 'Auction'], auction_stats['mean'], color='gold', alpha=0.7)
    axes[0,1].set_title('Average Order Value: Regular vs Auction')
    axes[0,1].set_ylabel('Average Order Value')
    
    # Refund rate analysis
    refund_by_month = df.groupby('month')['refunded'].mean() * 100
    axes[1,0].plot(refund_by_month.index, refund_by_month.values, marker='o', color='darkred', linewidth=2)
    axes[1,0].set_title('Refund Rate by Month (%)')
    axes[1,0].set_xlabel('Month')
    axes[1,0].set_ylabel('Refund Rate (%)')
    
    # API vs Manual order creation
    api_stats = df.groupby('is_created_by_api')['sum'].agg(['count', 'mean'])
    axes[1,1].bar(['Manual', 'API'], api_stats['count'], color='teal', alpha=0.7)
    axes[1,1].set_title('Order Count: Manual vs API Creation')
    axes[1,1].set_ylabel('Number of Orders')
    
    plt.tight_layout()
    plt.show()

def regional_analysis(df):
    """Analyze regional patterns"""
    print("=== REGIONAL ANALYSIS ===")
    
    # Top regions by order count and value
    regional_stats = df.groupby('region_id').agg({
        'sum': ['count', 'mean', 'sum'],
        'views_count': 'mean',
        'order_cancelled': 'mean'
    }).round(2)
    
    regional_stats.columns = ['Order_Count', 'Avg_Order_Value', 'Total_Revenue', 'Avg_Views', 'Cancel_Rate']
    regional_stats = regional_stats.sort_values('Order_Count', ascending=False)
    
    # Top 15 regions
    top_regions = regional_stats.head(15)
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Top regions by order count
    axes[0,0].barh(range(len(top_regions)), top_regions['Order_Count'], color='lightblue')
    axes[0,0].set_title('Top 15 Regions by Order Count')
    axes[0,0].set_xlabel('Number of Orders')
    axes[0,0].set_yticks(range(len(top_regions)))
    axes[0,0].set_yticklabels(top_regions.index)
    
    # Top regions by total revenue
    top_revenue = regional_stats.sort_values('Total_Revenue', ascending=False).head(15)
    axes[0,1].barh(range(len(top_revenue)), top_revenue['Total_Revenue'], color='lightgreen')
    axes[0,1].set_title('Top 15 Regions by Total Revenue')
    axes[0,1].set_xlabel('Total Revenue')
    axes[0,1].set_yticks(range(len(top_revenue)))
    axes[0,1].set_yticklabels(top_revenue.index)
    
    # Average order value by region (top 15)
    top_avg = regional_stats.sort_values('Avg_Order_Value', ascending=False).head(15)
    axes[1,0].barh(range(len(top_avg)), top_avg['Avg_Order_Value'], color='orange')
    axes[1,0].set_title('Top 15 Regions by Average Order Value')
    axes[1,0].set_xlabel('Average Order Value')
    axes[1,0].set_yticks(range(len(top_avg)))
    axes[1,0].set_yticklabels(top_avg.index)
    
    # Cancellation rate by region (regions with >1000 orders)
    high_volume_regions = regional_stats[regional_stats['Order_Count'] > 1000].sort_values('Cancel_Rate', ascending=False)
    if len(high_volume_regions) > 0:
        axes[1,1].barh(range(len(high_volume_regions)), high_volume_regions['Cancel_Rate'] * 100, color='red', alpha=0.7)
        axes[1,1].set_title('Cancellation Rate by High-Volume Regions (%)')
        axes[1,1].set_xlabel('Cancellation Rate (%)')
        axes[1,1].set_yticks(range(len(high_volume_regions)))
        axes[1,1].set_yticklabels(high_volume_regions.index)
    
    plt.tight_layout()
    plt.show()
    
    print("Top 10 Regions Summary:")
    print(top_regions.head(10))

# =============================================================================
# 6. MULTIVARIATE ANALYSIS
# =============================================================================

def multivariate_analysis(df):
    """Perform multivariate analysis"""
    print("=== MULTIVARIATE ANALYSIS ===")
    
    # Create a sample for performance
    sample_df = df.sample(n=min(10000, len(df)), random_state=42)
    
    # 3D scatter plot: Views vs Order Amount vs Success Fee
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    scatter = ax.scatter(sample_df['views_count'], sample_df['sum'], sample_df['success_fee'], 
                        c=sample_df['month'], cmap='viridis', alpha=0.6)
    ax.set_xlabel('Views Count')
    ax.set_ylabel('Order Amount')
    ax.set_zlabel('Success Fee')
    ax.set_title('3D Scatter: Views vs Order Amount vs Success Fee (colored by month)')
    plt.colorbar(scatter)
    plt.show()
    
    # Analyze order patterns by multiple dimensions
    multi_analysis = df.groupby(['month', 'is_weekend', 'auction_winning']).agg({
        'sum': ['count', 'mean'],
        'views_count': 'mean',
        'order_cancelled': 'mean'
    }).round(2)
    
    print("Multi-dimensional Analysis (Month, Weekend, Auction):")
    print(multi_analysis.head(20))

# =============================================================================
# 7. KEY INSIGHTS AND SUMMARY
# =============================================================================

def generate_insights(df):
    """Generate key insights from the analysis"""
    print("=== KEY INSIGHTS AND FINDINGS ===")
    
    insights = []
    
    # Basic statistics
    total_orders = len(df)
    total_revenue = df['sum'].sum()
    avg_order_value = df['sum'].mean()
    
    insights.append(f"Dataset contains {total_orders:,} orders with total revenue of {total_revenue:,.2f}")
    insights.append(f"Average order value: {avg_order_value:.2f}")
    
    # Temporal insights
    monthly_orders = df.groupby('month').size()
    peak_month = monthly_orders.idxmax()
    low_month = monthly_orders.idxmin()
    insights.append(f"Peak month: {peak_month} ({monthly_orders[peak_month]:,} orders)")
    insights.append(f"Lowest month: {low_month} ({monthly_orders[low_month]:,} orders)")
    
    # Weekend vs weekday
    weekend_avg = df[df['is_weekend']]['sum'].mean()
    weekday_avg = df[~df['is_weekend']]['sum'].mean()
    if weekend_avg > weekday_avg:
        insights.append(f"Weekend orders have higher average value ({weekend_avg:.2f} vs {weekday_avg:.2f})")
    else:
        insights.append(f"Weekday orders have higher average value ({weekday_avg:.2f} vs {weekend_avg:.2f})")
    
    # Cancellation and refund rates
    cancel_rate = df['order_cancelled'].mean() * 100
    refund_rate = df['refunded'].mean() * 100
    insights.append(f"Order cancellation rate: {cancel_rate:.2f}%")
    insights.append(f"Refund rate: {refund_rate:.2f}%")
    
    # Auction vs regular orders
    auction_orders = df[df['auction_winning']].shape[0]
    auction_avg = df[df['auction_winning']]['sum'].mean()
    regular_avg = df[~df['auction_winning']]['sum'].mean()
    insights.append(f"Auction orders: {auction_orders:,} ({auction_orders/total_orders*100:.1f}%)")
    insights.append(f"Auction orders avg value: {auction_avg:.2f} vs Regular: {regular_avg:.2f}")
    
    # Views impact
    zero_views = (df['views_count'] == 0).sum()
    insights.append(f"Orders with zero views: {zero_views:,} ({zero_views/total_orders*100:.1f}%)")
    
    # Regional insights
    top_region = df.groupby('region_id').size().idxmax()
    top_region_orders = df.groupby('region_id').size().max()
    insights.append(f"Top region by orders: {top_region} ({top_region_orders:,} orders)")
    
    # API vs manual
    api_orders = df[df['is_created_by_api']].shape[0]
    insights.append(f"API-created orders: {api_orders:,} ({api_orders/total_orders*100:.1f}%)")
    
    print("\n".join([f"• {insight}" for insight in insights]))
    
    return insights

# =============================================================================
# MAIN EXECUTION
# =============================================================================

if __name__ == "__main__":
    print("Loading data for advanced analysis...")
    df = load_preprocessed_data()
    
    # Bivariate analysis
    correlation_analysis(df)
    temporal_analysis(df)
    categorical_analysis(df)
    regional_analysis(df)
    
    # Multivariate analysis
    multivariate_analysis(df)
    
    # Generate insights
    insights = generate_insights(df)
    
    print("\n=== COMPREHENSIVE EDA COMPLETED ===")
    print("All analysis sections have been completed successfully!")
