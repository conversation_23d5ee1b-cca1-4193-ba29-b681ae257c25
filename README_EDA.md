# Comprehensive Exploratory Data Analysis (EDA)
## Russian Orders Dataset (January - June 2025)

This repository contains a comprehensive exploratory data analysis of the Russian orders dataset spanning 6 months (January to June 2025) with approximately 242,000 transaction records.

## 📁 Files Overview

### Analysis Scripts
- **`comprehensive_eda.py`** - Core EDA functions (data loading, quality assessment, univariate analysis)
- **`eda_advanced_analysis.py`** - Advanced analysis (bivariate, multivariate, insights generation)
- **`run_eda.py`** - Main execution script that runs the complete analysis
- **`comprehensive_eda_notebook.ipynb`** - Jupyter notebook version for interactive analysis

### Data Files
- **`Data/orders_info_ru_01-2025.csv`** - January 2025 orders (43,740 records)
- **`Data/orders_info_ru_02-2025.csv`** - February 2025 orders (46,518 records)
- **`Data/orders_info_ru_03-2025.csv`** - March 2025 orders (47,264 records)
- **`Data/orders_info_ru_04-2025.csv`** - April 2025 orders (38,739 records)
- **`Data/orders_info_ru_05-2025.csv`** - May 2025 orders (35,931 records)
- **`Data/orders_info_ru_06-2025.csv`** - June 2025 orders (30,166 records)

## 🚀 Quick Start

### Prerequisites
```bash
pip install pandas numpy matplotlib seaborn plotly scipy
```

### Running the Analysis

#### Option 1: Complete Analysis (Recommended)
```bash
python run_eda.py
```

#### Option 2: Step-by-step Analysis
```bash
# Run basic EDA
python comprehensive_eda.py

# Run advanced analysis
python eda_advanced_analysis.py
```

#### Option 3: Interactive Jupyter Notebook
```bash
jupyter notebook comprehensive_eda_notebook.ipynb
```

## 📊 Analysis Components

### 1. Data Overview
- **Dataset Structure**: 24 columns including transaction, user, and business metrics
- **Data Types**: Mixed (numerical, categorical, boolean, datetime)
- **Memory Usage**: ~15-20 MB total
- **Time Range**: January 1, 2025 - June 30, 2025

### 2. Data Quality Assessment
- ✅ **Missing Values Analysis**: Identification and visualization of missing data
- ✅ **Duplicate Detection**: Check for duplicate records and IDs
- ✅ **Data Consistency**: Validation of data formats and ranges
- ✅ **Outlier Detection**: Statistical identification of anomalous values

### 3. Univariate Analysis
- **Order Amounts**: Distribution, skewness, outliers
- **Views Count**: Engagement patterns and zero-view analysis
- **Categorical Variables**: Frequency distributions and unique value counts
- **Temporal Variables**: Date component analysis

### 4. Bivariate Analysis
- **Correlation Matrix**: Relationships between numerical variables
- **Temporal Patterns**: Monthly, weekly, and daily trends
- **Categorical Relationships**: Cross-tabulations and chi-square tests
- **Regional Analysis**: Geographic distribution and performance

### 5. Multivariate Analysis
- **3D Visualizations**: Complex variable interactions
- **Segmentation Analysis**: Multi-dimensional grouping
- **Pattern Recognition**: Advanced relationship discovery

### 6. Key Insights Generation
- **Business Metrics**: Revenue, conversion rates, customer behavior
- **Temporal Trends**: Seasonal patterns and growth trends
- **Regional Insights**: Geographic performance variations
- **Operational Metrics**: Cancellation rates, refund patterns

## 📈 Key Dataset Features

### Transaction Data
- `id`: Unique order identifier
- `sum`: Order amount (revenue)
- `order_date`: Transaction timestamp
- `success_fee`: Platform commission

### Product & Engagement
- `category_id`: Product category
- `offer_id`: Specific product offer
- `views_count`: Product page views
- `auction_winning`: Auction vs. regular sale

### User Information
- **Sellers**: ID, rating, registration date, contact info, blocking status
- **Buyers**: ID, rating, registration date, contact info, blocking status

### Business Metrics
- `order_cancelled`: Cancellation status
- `refunded`: Refund status
- `is_created_by_api`: Order creation method
- `region_id`: Geographic location

## 📊 Expected Outputs

### Visualizations
- Distribution histograms and box plots
- Correlation heatmaps
- Time series plots
- Geographic analysis charts
- 3D scatter plots
- Pie charts and bar plots

### Statistical Summaries
- Descriptive statistics for all variables
- Correlation coefficients
- Skewness and kurtosis measures
- Frequency tables

### Business Insights
- Revenue trends and patterns
- Customer behavior analysis
- Regional performance metrics
- Operational efficiency indicators

### Reports
- `eda_summary_report.md`: Comprehensive findings summary
- Console output with detailed statistics
- Interactive plots (if using Plotly)

## 🔧 Customization

### Modifying Analysis Parameters
```python
# In comprehensive_eda.py, modify these parameters:
plt.rcParams['figure.figsize'] = (15, 10)  # Change plot sizes
sample_size = 5000  # Adjust sample size for performance
bins = 100  # Change histogram bins
```

### Adding Custom Analysis
```python
# Add new analysis functions to eda_advanced_analysis.py
def custom_analysis(df):
    # Your custom analysis code here
    pass
```

## 📋 Analysis Checklist

- ✅ **Data Loading**: Multi-file CSV loading and combination
- ✅ **Data Preprocessing**: Date parsing, boolean conversion, feature engineering
- ✅ **Quality Assessment**: Missing values, duplicates, outliers
- ✅ **Univariate Analysis**: Individual variable distributions
- ✅ **Bivariate Analysis**: Variable relationships and correlations
- ✅ **Multivariate Analysis**: Complex interactions
- ✅ **Temporal Analysis**: Time-based patterns
- ✅ **Regional Analysis**: Geographic insights
- ✅ **Business Insights**: Actionable findings
- ✅ **Visualization**: Comprehensive charts and plots
- ✅ **Documentation**: Detailed reporting

## 🚨 Troubleshooting

### Common Issues
1. **Missing Data Directory**: Ensure `Data/` folder exists with CSV files
2. **Import Errors**: Install required packages using pip
3. **Memory Issues**: Reduce sample sizes in analysis functions
4. **Plot Display**: Use `plt.show()` for static plots, Jupyter for interactive

### Performance Optimization
- Use data sampling for large datasets
- Reduce plot complexity for faster rendering
- Consider chunked processing for memory efficiency

## 📞 Support

For questions or issues with the analysis:
1. Check the console output for detailed error messages
2. Verify data file integrity and format
3. Ensure all required libraries are installed
4. Review the analysis parameters and customize as needed

---

**Generated by**: Augment Agent EDA System  
**Last Updated**: July 2025  
**Dataset**: Russian Orders (Jan-Jun 2025)
